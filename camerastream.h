#ifndef CAMERASTREAM_H
#define CAMERASTREAM_H

#include <QObject>
#include <QDateTime>
#include <QTimer>
#include <gst/app/gstappsink.h>
#include <gst/gst.h>

#define CAMERA "/dev/video11"
#define WIDTH 3840
#define HEIGHT 2160
#define FRAMERATE 60

// 前向声明
class MainWindow;

class CameraStream : public QObject
{
    Q_OBJECT
public:
    explicit CameraStream(QObject *parent = nullptr);
    ~CameraStream();
    bool start_camera();

    // 录像控制方法
    void startRecording();
    void stopRecording();
    bool isRecording = false;
    QString recordingfilename;

    // 水印控制方法
    void updateTimeWatermark();
    void restoreTimeWatermarkOnInit();

    // 拍照控制方法
    void takePhoto();

    // 清理摄像头数据
    void cleanupCameraData();

    // 通道索引（用于水印状态）
    int channelstream = 0;
private:
    // GStreamer管道
    GstElement *pipeline = nullptr;
    GstElement *flip=nullptr;

    // Parser元素管理
    GstElement *parser = nullptr;  // parser元素引用
    GstElement *decoder = nullptr; // decoder元素引用
    // 录像相关元素
    GstElement *tee = nullptr;        // 流分流器
    GstElement *queue_record = nullptr;
    GstElement *encoder = nullptr;
    GstElement *h264parse = nullptr;
    GstElement *muxer = nullptr;
    GstElement *filesink = nullptr;
    GstElement *appsink = nullptr;

    // 缓存的录像分支
    GstElement *m_cached_record_branch = nullptr;
    GstPad *m_record_tee_pad = nullptr;
    GstPad *m_record_queue_pad = nullptr;

    // 音频相关元素
    GstElement *m_audio_source = nullptr;      // alsasrc
    GstElement *m_audio_convert = nullptr;     // audioconvert
    GstElement *m_audio_encoder = nullptr;     // voaacenc
    GstElement *m_audio_parser = nullptr;      // aacparse
    GstElement *m_audio_queue = nullptr;       // queue for audio
    QString m_audio_device_path;               // 音频设备路径

    // 预览分支元素（用于水印隔离）
    GstElement *m_preview_convert = nullptr;      // 预览分支的videoconvert
    GstElement *m_preview_capsfilter = nullptr;   // 预览分支的capsfilter

    // 时间水印元素
    GstElement *m_timeoverlay = nullptr;          // 全局时间水印元素
    GstElement *m_convert = nullptr;              // videoconvert元素（用于时间水印插入点）

    // 拍照相关
    bool m_photoBranchCreated = false;         // 拍照分支是否已创建
    bool m_photoPipelineActive = false;        // 拍照管道是否激活
    bool m_photoRequested = false;             // 是否有拍照请求等待处理
    QTimer* m_photoDeactivateTimer = nullptr;  // 拍照管道延迟停用定时器

    // MainWindow指针
    MainWindow* m_mainWindow = nullptr;

    // 录像分支管理方法
    GstElement* createRecordBranch(const QString &filePath);
    void destroyRecordBranch();
    bool activateRecordBranch();
    bool deactivateRecordBranch();

    // 音频分支管理方法
    bool createAudioBranch(const QString &audioDevice);
    void destroyAudioBranch();
    bool activateAudioBranch();

    // 水印管理方法
    bool insertTimeWatermark();
    bool removeTimeWatermark();

    // 拍照分支管理方法
    GstElement* createPhotoSink();
    void destroyPhotoSink(GstElement* photo_sink);
    void activatePhotoPipeline();
    void deactivatePhotoPipeline();

    // 拍照回调函数
    static GstFlowReturn photoSampleCallback(GstElement *sink, gpointer data);

private slots:
    // 拍照管道延迟停用超时处理
    void onPhotoDeactivateTimeout();
    // 延迟启动定时器（在主线程中执行）
    void startDeactivateTimerInMainThread();

signals:
};

#endif // CAMERASTREAM_H
